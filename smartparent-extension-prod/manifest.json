{"manifest_version": 3, "name": "Smart Parental Control", "version": "3.1.0", "description": "AI-powered web filter & access control. Smart content analysis, screen time management, eye protection & more.", "permissions": ["storage", "alarms", "tabs", "history", "scripting"], "host_permissions": ["<all_urls>", "https://smartparent.qubitrhythm.com/*", "https://test.qubitrhythm.com/*"], "content_scripts": [{"matches": ["http://*/*", "https://*/*"], "js": ["content.js"], "run_at": "document_idle", "all_frames": false, "match_about_blank": false}], "externally_connectable": {"matches": ["<all_urls>"]}, "background": {"service_worker": "background.js"}, "action": {"default_popup": "popup/popup.html", "default_icon": {"48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["warning.html", "closed.html", "privacy.html", "subscribe.html", "subscribe.css", "subscribe.js", "success.html", "cancel.html", "icons/icon_floating.png"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "default-src 'self' data:; script-src 'self'; style-src 'self' 'unsafe-inline' data:; img-src 'self' data:; connect-src https://smartparent.qubitrhythm.com https://test.qubitrhythm.com https://extension.smartparent.qubitrhythm.com https://www.google-analytics.com/; frame-src https://www.youtube.com/; object-src 'none';"}}